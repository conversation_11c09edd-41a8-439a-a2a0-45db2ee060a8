import { useState } from "react";
import { SearchStatus, Filter, MessageQuestion } from 'iconsax-react';

export const GuestTab = () => {
  const [activeTab, setActiveTab] = useState('Confirmed');
  const [hoveredCardId, setHoveredCardId] = useState<number | null>(null);
  
  const guests = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '07015263711',
      status: 'Attending',
      addedBy: 'Added Manually',
      avatar: 'OR',
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '07023456789',
      status: 'Attending',
      addedBy: 'Added Via Email Invite',
      avatar: 'OR',
    },
    {
      id: 3,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '07034567890',
      status: 'Attending',
      addedBy: 'Added Manually',
      avatar: 'OR',
    },
    {
      id: 4,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '07045678901',
      status: 'Attending',
      addedBy: 'Added Via Email Invite',
      avatar: '<PERSON>',
    },
    {
      id: 5,
      name: '<PERSON> Rhye',
      email: '<EMAIL>',
      phone: '07056789012',
      status: 'Attending',
      addedBy: 'Added via Bulk Upload',
      avatar: 'OR',
    },
    {
      id: 6,
      name: '<PERSON> Rhye',
      email: '<EMAIL>',
      phone: '07067890123',
      status: 'Attending',
      addedBy: 'Added Manually',
      avatar: 'OR',
    },
  ];

  const awaitingGuests = [
    {
      id: 7,
      name: 'Awaiting Response...',
      email: '<EMAIL>',
      status: 'Pending',
      addedBy: 'Added Via Email invite',
      avatar: '?',
      phone: undefined,
    },
    {
      id: 8,
      name: 'Awaiting Response...',
      email: '<EMAIL>',
      status: 'Pending',
      addedBy: 'Added Via Email invite',
      avatar: '?',
      phone: undefined,
    },
    {
      id: 9,
      name: 'Awaiting Response...',
      email: '<EMAIL>',
      status: 'Pending',
      addedBy: 'Added Via Email invite',
      avatar: '?',
      phone: undefined,
    },
    {
      id: 10,
      name: 'Awaiting Response...',
      email: '<EMAIL>',
      status: 'Pending',
      addedBy: 'Added Via Email invite',
      avatar: '?',
      phone: undefined,
    },
    {
      id: 11,
      name: 'Awaiting Response...',
      email: '<EMAIL>',
      status: 'Pending',
      addedBy: 'Added Via Email invite',
      avatar: '?',
      phone: undefined,
    },
    {
      id: 12,
      name: 'Awaiting Response...',
      email: '<EMAIL>',
      status: 'Pending',
      addedBy: 'Added Via Email invite',
      avatar: '?',
      phone: undefined,
    },
  ];
  
  const declinedGuests = [
    {
      id: 13,
      name: 'No Response...',
      email: '<EMAIL>',
      status: 'Declined',
      addedBy: 'Added Via email Invite',
      avatar: '?',
      phone: undefined,
    },
    {
      id: 14,
      name: 'No Response...',
      email: '<EMAIL>',
      status: 'Declined',
      addedBy: 'Added Via email Invite',
      avatar: '?',
      phone: undefined,
    },
    {
      id: 15,
      name: 'No Response...',
      email: '<EMAIL>',
      status: 'Declined',
      addedBy: 'Added Via email Invite',
      avatar: '?',
      phone: undefined,
    },
    {
      id: 16,
      name: 'Olivia Rhye',
      email: '<EMAIL>',
      phone: '07015263711',
      status: 'Declined',
      addedBy: 'Added Via email Invite',
      avatar: 'OR',
    },
    {
      id: 17,
      name: 'Olivia Rhye',
      email: '<EMAIL>',
      phone: '07015263711',
      status: 'Declined',
      addedBy: 'Added Via email Invite',
      avatar: 'OR',
    },
    {
      id: 18,
      name: 'Olivia Rhye',
      email: '<EMAIL>',
      phone: '07015263711',
      status: 'Declined',
      addedBy: 'Added Via email Invite',
      avatar: 'OR',
    },
  ];

  const displayedGuests =
    activeTab === 'Confirmed'
      ? guests
      : activeTab === 'Pending'
      ? awaitingGuests
      : activeTab === 'Declined'
      ? declinedGuests
      : [];
  return (
    <div className=" mb-16">
      <div className="flex justify-between items-center mb-6 relative">
        <div className="flex bg-black/3 rounded-full p-1.5">
          {['Confirmed', 'Pending', 'Declined'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`md:px-4 px-1 py-1 md:py-[9px] cursor-pointer rounded-full text-sm  ${
                activeTab === tab
                  ? 'bg-white text-primary font-bold'
                  : 'text-grey-250 font-medium'
              }`}>
              {tab}
            </button>
          ))}
        </div>
        <div
          className={`absolute bottom-[-10px] w-1 h-1 bg-cus-orange rounded-full ${
            activeTab === 'Confirmed'
              ? 'left-[50px]'
              : activeTab === 'Pending'
              ? 'md:left-[142px] left-[108px]'
              : 'md:left-[228px] left-[170px]'
          }`}
        />
        <div className="flex space-x-3">
          <button className="md:h-12 md:w-12 h-7 w-7 rounded-full bg-white flex items-center justify-center">
            <SearchStatus size={26} color="#4D55F2" variant="Bulk" />
          </button>
          <button className="md:h-12 md:w-12  h-7 w-7 rounded-full bg-white flex items-center justify-center">
            <Filter size={26} color="#5F66F3" variant="Bulk" />
          </button>
        </div>
      </div>

      <div className="space-y-4 ">
        {displayedGuests.map((guest) => (
          <div
            key={guest.id}
            className="p-4 bg-white  border border-grey-150 rounded-2xl md:flex justify-between items-center"
            onMouseEnter={() => setHoveredCardId(guest.id)}
            onMouseLeave={() => setHoveredCardId(null)}>
            <div className="flex items-center">
              <div
                className={`w-10 h-10 ${
                  guest.avatar === '?'
                    ? 'bg-primary-150'
                    : 'bg-gradient-to-br from-[#FEF7F4]'
                } rounded-full flex items-center justify-center mr-4 text-dark-blue-200 font-semibold text-base`}>
                {guest.avatar === '?' ? (
                  <MessageQuestion size={24} color="#9499F7" variant="Bulk" />
                ) : (
                  guest.avatar
                )}
              </div>
              <div>
                <h3
                  className={`text-dark-blue-100 ${
                    guest.avatar === '?' && 'italic text-grey-550 !font-normal'
                  } text-sm font-semibold`}>
                  {guest.name}
                </h3>
                <p className="text-grey-650 text-xs">
                  {guest.email} {guest?.phone && `• ${guest.phone}`}
                </p>
              </div>
            </div>
            <div className="flex items-start justify-end mt-5 md:mt-0">
              <div className="flex flex-col items-end mr-2">
                <span
                  className={`${
                    guest.status === 'Attending'
                      ? 'text-green-600 bg-grin '
                      : guest.status === 'Pending'
                      ? 'text-cus-orange-600 bg-cus-pink-150 '
                      : 'text-cus-red-100 bg-cus-pink-250'
                  } font-medium px-2.5 py-0.5 rounded-2xl text-sm`}>
                  {guest.status}
                </span>
                <span className="text-grey-650 text-[10px] italic text-right md:text-start">
                  {guest.addedBy}
                </span>
              </div>
              {hoveredCardId === guest.id && (
                <button className="w-6 h-6 cursor-pointer rounded-full bg-gray-100 flex items-center justify-center transition-all duration-500 ease-in-out">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 32 32"
                    fill="#999999">
                    <circle cx="8" cy="16" r="3" />
                    <circle cx="16" cy="16" r="3" />
                    <circle cx="24" cy="16" r="3" />
                  </svg>
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
