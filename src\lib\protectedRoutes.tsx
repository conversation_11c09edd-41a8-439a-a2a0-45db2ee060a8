import { Navigate, Outlet, useNavigate } from 'react-router-dom';
import { ReactNode, useEffect, useState } from 'react';
import { isTokenValid } from './helpers';
import { useUserAuthStore } from './store/auth';
import { AuthServices } from './services/auth';

export const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const { clearAuthData } = useUserAuthStore();
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (!token || (token && !isTokenValid(token))) {
      if (token) {
        clearAuthData();
        console.error('Session expired, please login again');
      }
      navigate('/login');
    }
  }, [token, navigate, clearAuthData]);

  if (!token || (token && !isTokenValid(token))) {
    return null;
  }

  return children;
};

export const AuthRoute = () => {
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();

  return token ? <Navigate to="/" replace /> : <Outlet />;
};

export const OnboardingRoute = ({ children }: { children: ReactNode }) => {
  const { clearAuthData } = useUserAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);
  const navigate = useNavigate();

  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (!token || !isTokenValid(token)) {
        clearAuthData();
        navigate('/login');
        return;
      }

      try {
        const response = await AuthServices.getUser();
        const completedOnboarding =
          response?.data?.config?.completed_onboarding === true;

        setHasCompletedOnboarding(completedOnboarding);
        if (completedOnboarding) {
          navigate('/', { replace: true });
          return;
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkOnboardingStatus();
  }, [token, navigate, clearAuthData]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (hasCompletedOnboarding) {
    return null;
  }
  if (!token || !isTokenValid(token)) {
    return null;
  }

  return children;
};
