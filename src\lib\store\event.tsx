import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export interface CreatedEventData {
  id: string;
  title: string;
  category_id: string;
  category_name?: string;
  date_from: string;
  date_to: string;
  location_address: string;
  banner_image_id: string | null;
  status?: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
}

interface EventsMetaData {
  from: number | null;
  to: number | null;
  page: number;
  per_page: number;
  previous_page: boolean;
  next_page: boolean;
  page_count: number;
  total: number;
}

type EventState = {
  createdEventData: CreatedEventData | null;
  userEvents: CreatedEventData[];
  selectedEvent: CreatedEventData | null;
  eventsMeta: EventsMetaData | null;
  setCreatedEventData: (eventData: CreatedEventData) => void;
  setUserEvents: (events: CreatedEventData[], meta?: EventsMetaData) => void;
  setSelectedEvent: (event: CreatedEventData) => void;
  clearCreatedEventData: () => void;
};

export const useEventStore = create<EventState>()(
  persist(
    (set, get) => ({
      createdEventData: null,
      userEvents: [],
      selectedEvent: null,
      eventsMeta: null,
      setCreatedEventData: (eventData) => set({ createdEventData: eventData }),
      setUserEvents: (events, meta) => {
        const currentState = get();
        const newSelectedEvent =
          currentState.selectedEvent || (events.length > 0 ? events[0] : null);

        set({
          userEvents: events,
          eventsMeta: meta || null,
          selectedEvent: newSelectedEvent,
        });
      },
      setSelectedEvent: (event) => set({ selectedEvent: event }),
      clearCreatedEventData: () => set({ createdEventData: null }),
    }),
    {
      name: 'event-store',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
