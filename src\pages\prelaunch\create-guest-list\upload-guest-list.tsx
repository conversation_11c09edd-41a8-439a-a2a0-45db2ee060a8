import { useState, useRef, useEffect } from 'react';
import { TickCircle, Notepad2 } from 'iconsax-react';
import { Icon } from '../../../components/icons/icon';
import scan from '../../../assets/animations/scanniing.gif';

interface UploadGuestListProps {
  onNextStep?: () => void;
  onFormActiveChange?: (isActive: boolean) => void;
}

export const UploadGuestList = ({
  onNextStep,
  onFormActiveChange,
}: UploadGuestListProps) => {
  // const [file, setFile] = useState<File | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [recordsCount, setRecordsCount] = useState<number>(0);
  // const [hasErrors, setHasErrors] = useState<boolean>(false);
  const [isComplete, setIsComplete] = useState<boolean>(false);
  const [fileError, setFileError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    onFormActiveChange?.(isUploading);
  }, [isUploading, onFormActiveChange]);

  const isValidFileType = (file: File): boolean => {
    const validTypes = [
      '.csv',
      '.xlsx',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
    ];
    return validTypes.some(
      (type) => file.name.toLowerCase().endsWith(type) || file.type === type
    );
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Reset states
      setFileError('');
      // setIsComplete(false);

      // Check if file type is valid
      if (!isValidFileType(selectedFile)) {
        setFileError('Invalid file type. Please upload a CSV or XLSX file.');
        return;
      }

      // setFile(selectedFile);
      setFileName(selectedFile.name);
      setIsUploading(true);

      // Simulate file processing
      setTimeout(() => {
        setIsUploading(false);
        setIsScanning(true);

        // Simulate scanning
        setTimeout(() => {
          setIsScanning(false);
          setRecordsCount(160); // Example count
          // setHasErrors(false);
          setIsComplete(true);
        }, 2000);
      }, 1000);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files?.[0];
    if (droppedFile) {
      // Reset states
      setFileError('');
      // setIsComplete(false);

      // Check if file type is valid
      if (!isValidFileType(droppedFile)) {
        setFileError('Invalid file type. Please upload a CSV or XLSX file.');
        return;
      }

      // setFile(droppedFile);
      // setFileName(droppedFile.name);
      setIsUploading(true);

      // Simulate file processing
      setTimeout(() => {
        setIsUploading(false);
        setIsScanning(true);

        // Simulate scanning
        setTimeout(() => {
          setIsScanning(false);
          setRecordsCount(160); // Example count
          // setHasErrors(false);
          // setIsComplete(true);
        }, 2000);
      }, 1000);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleUploadRecord = () => {
    if (isComplete && onNextStep) {
      onNextStep();
    }
  };

  const handleDownloadTemplate = () => {
    console.log('Downloading template');
  };

  return (
    <div className="flex-1 pt-8 px-2 md:px-0">
      <h3 className="md:text-[28px] text-lg font-medium">Upload Guest list</h3>
      <p className="md:text-base text-sm text-grey-250 mb-5">
        Got a documented list? upload right away!
      </p>

      <div className="mb-4 text-xs relative">
        <div className="bg-primary-150  py-1.5 pl-3 pr-12 rounded-2xl w-fit">
          <span className="text-primary-500 font-medium">
            Hey!, we helped you with a template
          </span>
        </div>
        <button
          onClick={handleDownloadTemplate}
          className="md:absolute -right-1 top-[3px] text-primary flex items-center gap-1 rounded-full py-0.5 px-2 bg-white border border-primary-950 italic font-bold">
          Download Template
          <Icon name="helpCircle" />
        </button>
      </div>
      <div
        className={` ${
          isComplete ? 'bg-primary-150' : 'bg-grey-350'
        } flex flex-col md:flex-row rounded-2xl  mb-2 cursor-pointer`}
        onClick={handleUploadClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}>
        <div
          className={` ${
            isComplete ? 'md:bg-primary-250' : 'md:bg-grey-850'
          } px-2 h-full mx-auto py-4 rounded-l-2xl`}>
          <Notepad2
            size={90}
            color={isComplete ? '#B8BBFA' : '#B3B3B3'}
            variant="Bulk"
            className="opacity-70"
          />
        </div>
        <div className="flex flex-col justify-between  w-full text-center md:text-start">
          <div className="md:ml-4 my-6">
            <h3
              className={`italic text-base font-medium ${
                isComplete ? 'text-primary-750' : 'text-black'
              }`}>
              {isComplete ? <> {fileName}</> : ' No file Uploaded yet '}
            </h3>
            <p
              className={`text-xs ${
                isComplete ? 'text-primary-500' : 'text-grey-550 '
              }`}>
              {isComplete
                ? 'Click to upload or change document'
                : 'Click to upload document'}
            </p>
          </div>
          <p
            className={`text-[10px] italic  py-[7px] rounded-br-2xl  ${
              isComplete
                ? 'text-primary-500 bg-primary-250'
                : 'text-dark-200 md:bg-grey-850'
            }`}>
            <span className="font-extrabold">Note:</span> Acceptable docs
            include (.CSV, .XLSX)
          </p>
        </div>
      </div>
      {fileError && <p className="text-red-500 text-sm mb-4">{fileError}</p>}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
      />

      {isScanning ||
        (isUploading && (
          <div className="flex items-center gap-2 border border-grey-150 w-fit py-1 px-3 rounded-full">
            <img src={scan} alt="scanning" className="h-4 w-4" />{' '}
            <p className="text-grey-500 text-sm font-medium">
              Scanning Document...
            </p>
          </div>
        ))}

      {isComplete && (
        <div className="flex items-center gap-2 italic font-semibold text-sm">
          <p className="text-grey-550  py-1 px-3 rounded-full bg-grey-850">
            {recordsCount} Records Found
          </p>
          <div className="flex items-center gap-2 text-green-600 bg-green-50 px-3 py-1 rounded-full">
            <TickCircle size={16} color="#22C55E" variant="Bold" />
            <span className="text-sm font-medium">No Errors found</span>
          </div>
        </div>
      )}

      <div className="mt-38 py-3.5 border-t border-grey-850 flex justify-end">
        <button
          onClick={handleUploadRecord}
          disabled={!isComplete}
          className={`bg-primary text-white font-semibold py-3 px-6 rounded-full ${
            !isComplete
              ? 'opacity-50 cursor-not-allowed'
              : 'cursor-pointer hover:bg-primary/90'
          }`}>
          Upload Guest Record
        </button>
      </div>
    </div>
  );
};
