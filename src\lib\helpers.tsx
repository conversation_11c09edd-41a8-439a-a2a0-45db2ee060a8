import { jwtDecode } from 'jwt-decode';
interface JwtPayload {
  exp: number;
  [key: string]: unknown;
}

export const isTokenValid = (token: string): boolean => {
  try {
    if (!token) return false;
    const decoded = jwtDecode<JwtPayload>(token);
    return Date.now() < decoded.exp * 1000;
  } catch (error) {
    console.error('Token validation failed:', error);
    return false;
  }
};

export const getTokenExpiration = (token: string): Date | null => {
  try {
    const decoded = jwtDecode<JwtPayload>(token);
    return new Date(decoded.exp * 1000);
  } catch (error) {
    console.error('Token decoding failed:', error);
    return null;
  }
};

export const formatTime = (seconds: number | null) => {
  if (seconds === null) return '00:00';
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`;
};


