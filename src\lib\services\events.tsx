import { EventParkAPI } from '../event-park-api';

export interface EventCategories {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface CreateEventPayload {
  category_id: string;
  date_from: string;
  date_to: string;
  location_address: string;
  title: string;
}

export interface GuestData {
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
}

export interface CreateGuestPayload {
  eventId: string;
  guests?: GuestData[];
  file?: File;
}

export const events = {
  getEventCategories: async () => {
    return await EventParkAPI().get('/v1/events/categories');
  },
  createEvent: async (eventData: CreateEventPayload) => {
    return await EventParkAPI().post('/v1/user/events', eventData);
  },
  getEventForAuthUsers: async (params?: {
    page?: number;
    per_page?: number;
    from?: string;
    to?: string;
    status?: string;
  }) => {
    return await EventParkAPI().get('/v1/user/events', { params });
  },
  createGuestForAuthUsers: async (payload: CreateGuestPayload) => {
    const { eventId, guests, file } = payload;
    const formData = new FormData();
    formData.append('id', eventId);

    if (file) {
      formData.append('file', file);
    } else if (guests) {
      formData.append('guests', JSON.stringify(guests));
    } else {
      throw new Error('Either guests array or file must be provided');
    }

    return await EventParkAPI().post(
      `/v1/user/events/${eventId}/guests`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  },
};
