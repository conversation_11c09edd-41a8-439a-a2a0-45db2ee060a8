/* eslint-disable @typescript-eslint/no-explicit-any */
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useEventStore, CreatedEventData } from '../store/event';
import { events } from '../services/events';
import { useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { eventDebugUtils } from '../utils/eventDebug';

export const useEventManagement = () => {
  const {
    userEvents,
    selectedEvent,
    setSelectedEvent,
    setUserEvents,
    clearAllEventData: clearStoreData,
  } = useEventStore();
  const queryClient = useQueryClient();

  // Main query for fetching user events
  const { data, isLoading, isError, error, refetch, isFetching } = useQuery({
    queryKey: ['userEvents'],
    queryFn: () => events.getEventForAuthUsers(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnMount: 'always',
    refetchOnWindowFocus: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Update store when data changes - ALWAYS sync with API response
  useEffect(() => {
    if (data?.data) {
      // Always update with fresh API data, even if events array is empty
      const events = data.data.events || [];
      const meta = data.data.meta || null;

      // Debug logging to track data synchronization
      console.log('🔄 Syncing events with API response:', {
        apiEvents: events.length,
        storeEvents: userEvents.length,
        apiMeta: meta,
        timestamp: new Date().toISOString(),
      });

      // Compare with localStorage for debugging
      eventDebugUtils.compareApiWithStorage(data);

      setUserEvents(events, meta);

      // If we have no events and there was a selected event, clear it
      if (events.length === 0 && selectedEvent) {
        console.log('🧹 Clearing selected event as no events available');
        setSelectedEvent(null);
      }
    }
  }, [data, setUserEvents, selectedEvent, setSelectedEvent, userEvents.length]);

  // Handle error state
  useEffect(() => {
    if (isError) {
      console.error('Failed to fetch user events:', error);
      toast.error('Failed to load events. Please try again.');
    }
  }, [isError, error]);

  // Function to invalidate and refetch events
  const refreshEvents = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: ['userEvents'] });
    return refetch();
  }, [queryClient, refetch]);

  // Function to clear all event data (useful for logout or data reset)
  const clearAllEventData = useCallback(() => {
    queryClient.removeQueries({ queryKey: ['userEvents'] });
    clearStoreData();
  }, [queryClient, clearStoreData]);

  // Function to add a new event to the cache optimistically
  const addEventOptimistically = useCallback(
    (newEvent: CreatedEventData) => {
      queryClient.setQueryData(['userEvents'], (oldData: any) => {
        if (!oldData?.data?.events) return oldData;

        return {
          ...oldData,
          data: {
            ...oldData.data,
            events: [newEvent, ...oldData.data.events],
            meta: {
              ...oldData.data.meta,
              total: (oldData.data.meta?.total || 0) + 1,
            },
          },
        };
      });

      // Update the store as well
      const updatedEvents = [newEvent, ...userEvents];
      setUserEvents(updatedEvents, data?.data?.meta);

      // Set the new event as selected if no event is currently selected
      if (!selectedEvent) {
        setSelectedEvent(newEvent);
      }
    },
    [
      queryClient,
      userEvents,
      data?.data?.meta,
      setUserEvents,
      selectedEvent,
      setSelectedEvent,
    ]
  );

  // Function to remove an event from cache
  const removeEventOptimistically = useCallback(
    (eventId: string) => {
      queryClient.setQueryData(['userEvents'], (oldData: any) => {
        if (!oldData?.data?.events) return oldData;

        const filteredEvents = oldData.data.events.filter(
          (event: CreatedEventData) => event.id !== eventId
        );

        return {
          ...oldData,
          data: {
            ...oldData.data,
            events: filteredEvents,
            meta: {
              ...oldData.data.meta,
              total: Math.max((oldData.data.meta?.total || 0) - 1, 0),
            },
          },
        };
      });

      // Update the store as well
      const filteredEvents = userEvents.filter((event) => event.id !== eventId);
      setUserEvents(filteredEvents, data?.data?.meta);

      // If the removed event was selected, select the first available event
      if (selectedEvent?.id === eventId && filteredEvents.length > 0) {
        setSelectedEvent(filteredEvents[0]);
      } else if (selectedEvent?.id === eventId) {
        setSelectedEvent(null);
      }
    },
    [
      queryClient,
      userEvents,
      data?.data?.meta,
      setUserEvents,
      selectedEvent,
      setSelectedEvent,
    ]
  );

  // Function to update an event in cache
  const updateEventOptimistically = useCallback(
    (updatedEvent: CreatedEventData) => {
      queryClient.setQueryData(['userEvents'], (oldData: any) => {
        if (!oldData?.data?.events) return oldData;

        const updatedEvents = oldData.data.events.map(
          (event: CreatedEventData) =>
            event.id === updatedEvent.id ? updatedEvent : event
        );

        return {
          ...oldData,
          data: {
            ...oldData.data,
            events: updatedEvents,
          },
        };
      });

      // Update the store as well
      const updatedEvents = userEvents.map((event) =>
        event.id === updatedEvent.id ? updatedEvent : event
      );
      setUserEvents(updatedEvents, data?.data?.meta);

      // Update selected event if it's the one being updated
      if (selectedEvent?.id === updatedEvent.id) {
        setSelectedEvent(updatedEvent);
      }
    },
    [
      queryClient,
      userEvents,
      data?.data?.meta,
      setUserEvents,
      selectedEvent,
      setSelectedEvent,
    ]
  );

  // Mutation for creating events with optimistic updates
  const createEventMutation = useMutation({
    mutationFn: events.createEvent,
    onSuccess: (response) => {
      const newEvent = response.data;
      addEventOptimistically(newEvent);
      toast.success('Event created successfully!');
    },
    onError: (error: any) => {
      console.error('Error creating event:', error);
      toast.error(error?.response?.data?.message || 'Failed to create event');
      // Refresh events to ensure consistency
      refreshEvents();
    },
  });

  return {
    // Data
    userEvents,
    selectedEvent,
    isLoading,
    isError,
    isFetching,
    error,

    // Actions
    setSelectedEvent,
    refreshEvents,
    clearAllEventData,
    addEventOptimistically,
    removeEventOptimistically,
    updateEventOptimistically,
    createEventMutation,

    // Raw query data for advanced use cases
    rawData: data,
    refetch,

    // Debug utilities
    debugUtils: eventDebugUtils,
  };
};
